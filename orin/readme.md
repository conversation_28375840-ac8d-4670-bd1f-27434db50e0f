# System setup
```
sudo apt update
sudo apt --fix-broken install
sudo apt install nano
```
Set a fixed IP address through GUI.
```
IP: ************
netmask: *************
gateway: *************
dns: *************
```
Make filesystem resilient to random shutdowns.
```
./config_file_system.sh
```
Optional: run this script to check file system status. It was generated by the last script. Kinda wrthless maybe.
```
sudo /usr/local/bin/check-power-safe
```


# CAN setup
For jetson, I installed the standard stuff, then installed the PEAK can driver.
https://www.peak-system.com/fileadmin/media/linux/files/PCAN-Driver-Linux_UserMan_eng.pdf
Inside the "peak-linux-driver-8.20.0 folder:
`make clean`
`make netdev`
`sudo make install`

# set up CAN-FD params for netdev / SocketCAN
# Use parameters from Moteus docs for 80MHz clocks - https://github.com/otaviogood/moteus/blob/main/docs/reference.md
sudo ip link set can3 type can bitrate 1000000 sjw 10 sample-point 0.666 dbitrate 5000000 dsjw 5 dsample-point 0.666 fd on restart-ms 1000
sudo ip link set can3 up

# check that everything is working:
ip -details link show can3
ip -s link show can3

# Install poetry
`curl -sSL https://install.python-poetry.org | python3 -`
Add the path to .bashrc
`export PATH="$HOME/.local/bin:$PATH"`

XXX NOT USING DOCKER FOR NOW XXX Then run the Docker container. Instructions are at top of Dockerfile.

## project setup ##
I set up the project with:

`poetry init`
`poetry add python-can`

Then run with
(Maybe do `poetry update`) container doesn't have it by default i guess????

`poetry run python canjank.py`
