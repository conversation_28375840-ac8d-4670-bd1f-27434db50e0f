#!/bin/bash

# Nvidia Orin Power-Safe Configuration Script
# This script configures the system to handle sudden power loss without corruption
# while maintaining the ability to write and modify files

set -e

# ===== CONFIGURATION VARIABLES =====
# Auto-detect root device and UUID
ROOT_DEVICE=$(findmnt -n -o SOURCE /)
ROOT_UUID=$(blkid -s UUID -o value $ROOT_DEVICE)
ROOT_PARTITION_LABEL=$(blkid -s LABEL -o value $ROOT_DEVICE)
USERNAME=$(logname || echo "otaviogood")  # Try to get actual username

# Verify detected values
echo "Detected system configuration:"
echo "  Root device: $ROOT_DEVICE"
echo "  Root UUID: $ROOT_UUID"
echo "  Root label: $ROOT_PARTITION_LABEL"
echo "  Username: $USERNAME"
echo ""
echo "If these values are incorrect, press Ctrl+C and edit the script manually."

# ===== SCRIPT START =====
echo "======================================"
echo "Nvidia Orin Power-Safe Configuration"
echo "======================================"
echo ""
echo "This script will configure your system for safe sudden power-off."
echo "Docker, Snaps, and Updates will remain available for manual use."
echo "Press Ctrl+C to cancel, or Enter to continue..."
read

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo "Please run as root (use sudo)"
    exit 1
fi

# Backup current configurations
echo "Creating backup directory..."
BACKUP_DIR="/root/power-safe-backup"
mkdir -p $BACKUP_DIR

# Create timestamped backups if files exist and haven't been backed up
for file in /etc/fstab /etc/sysctl.conf /etc/systemd/journald.conf; do
    if [ -f "$file" ] && [ ! -f "$BACKUP_DIR/$(basename $file).original" ]; then
        cp "$file" "$BACKUP_DIR/$(basename $file).original"
        echo "  Backed up $file"
    fi
done

echo "Backups saved to $BACKUP_DIR/"
echo ""

# 1. Configure immediate writes - disable all write caching
echo "Configuring immediate disk writes..."
# Check if already configured
if ! grep -q "Power-safe configuration" /etc/sysctl.conf 2>/dev/null; then
    cat >> /etc/sysctl.conf << EOF

# Power-safe configuration - force immediate writes
vm.dirty_background_ratio = 0
vm.dirty_ratio = 0
vm.dirty_expire_centisecs = 0
vm.dirty_writeback_centisecs = 0
EOF
else
    echo "  Already configured in /etc/sysctl.conf"
fi

# Apply immediately
sysctl -p

# 2. Update fstab for synchronous writes
echo "Updating /etc/fstab for synchronous writes..."
# Check if already configured
if ! grep -q "sync,commit=1" /etc/fstab; then
    # Backup and update fstab
    cp /etc/fstab /etc/fstab.bak.$(date +%Y%m%d_%H%M%S)
    sed -i "s|UUID=${ROOT_UUID} / ext4 .*|UUID=${ROOT_UUID} / ext4 sync,commit=1,errors=remount-ro 0 1|" /etc/fstab
else
    echo "  Already configured in /etc/fstab"
fi

# 3. Configure journald for volatile logging
echo "Configuring systemd-journald for RAM-only logging..."
cat > /etc/systemd/journald.conf << EOF
[Journal]
Storage=volatile
RuntimeMaxUse=64M
RuntimeMaxFileSize=8M
EOF

# 4. Disable swap
echo "Disabling swap..."
swapoff -a
systemctl mask swap.target

# Remove swap entries from fstab
sed -i '/swap/d' /etc/fstab

# 5. Disable only automatic/background services
echo "Disabling automatic background services..."

# System logging services
systemctl disable rsyslog || true
systemctl stop rsyslog || true
systemctl mask systemd-journald-audit.socket || true

# Disable ONLY automatic updates, not the ability to update
echo "Disabling automatic updates (manual updates still available)..."
systemctl disable apt-daily.timer || true
systemctl disable apt-daily-upgrade.timer || true
systemctl stop apt-daily.timer || true
systemctl stop apt-daily-upgrade.timer || true

# Disable unattended upgrades
cat > /etc/apt/apt.conf.d/20auto-upgrades << EOF
APT::Periodic::Update-Package-Lists "0";
APT::Periodic::Unattended-Upgrade "0";
APT::Periodic::Download-Upgradeable-Packages "0";
APT::Periodic::AutocleanInterval "0";
EOF

# For snap, only disable auto-refresh
echo "Configuring snap to disable auto-refresh..."
snap set system refresh.hold="$(date --date='10 years' --iso-8601=seconds)"
snap set system refresh.metered=hold

# Docker - just disable auto-start, keep it available for manual use
echo "Disabling Docker auto-start (manual use still available)..."
systemctl disable docker || true
systemctl stop docker || true
# Don't disable containerd as Docker needs it when manually started

# PackageKit - disable but don't remove
systemctl disable packagekit || true
systemctl stop packagekit || true

# 6. Configure update notifier (disable auto-check but keep installed)
echo "Disabling update notifier auto-checks..."
sudo -u $USERNAME dconf write /com/ubuntu/update-notifier/no-show-notifications true 2>/dev/null || true
sudo -u $USERNAME gsettings set com.ubuntu.update-notifier no-show-notifications true 2>/dev/null || true

# 7. Disable GNOME services that write frequently
echo "Configuring GNOME services..."
sudo -u $USERNAME bash << 'EOF'
# Disable evolution data server autostart
gsettings set org.gnome.evolution-data-server autostart false || true

# Disable GNOME Software auto-updates but keep it functional
gsettings set org.gnome.software download-updates false || true
gsettings set org.gnome.software allow-updates false || true
gsettings set org.gnome.software refresh-when-metered false || true

# Disable tracker file indexing
systemctl --user disable tracker-miner-fs-3.service || true
systemctl --user stop tracker-miner-fs-3.service || true
systemctl --user mask tracker-miner-fs-3.service || true
EOF

# 8. Set filesystem mount options
echo "Remounting filesystem with sync option..."
mount -o remount,sync,commit=1 /

# 9. Verify ext4 journaling is enabled
echo "Verifying ext4 journaling..."
tune2fs -l $ROOT_DEVICE | grep -i "journal"

# 10. Create a script to check system status
cat > /usr/local/bin/check-power-safe << SCRIPT
#!/bin/bash
echo "=== Power-Safe Configuration Status ==="
echo ""
echo "Write cache settings:"
sysctl vm.dirty_background_ratio vm.dirty_ratio vm.dirty_expire_centisecs vm.dirty_writeback_centisecs
echo ""
echo "Mount options:"
mount | grep $ROOT_DEVICE
echo ""
echo "Swap status:"
swapon -s
echo ""
echo "Service status:"
echo -n "rsyslog: "; systemctl is-active rsyslog || echo "inactive"
echo -n "apt-daily.timer: "; systemctl is-active apt-daily.timer || echo "inactive"
echo -n "docker: "; systemctl is-enabled docker || echo "disabled"
echo -n "snapd: "; systemctl is-active snapd || echo "inactive"
echo ""
echo "Snap refresh status:"
snap refresh --time
echo ""
echo "To manually run updates: sudo apt update && sudo apt upgrade"
echo "To manually refresh snaps: sudo snap refresh"
echo "To manually start docker: sudo systemctl start docker"
SCRIPT

chmod +x /usr/local/bin/check-power-safe

# 11. Create helper scripts for manual operations
cat > /usr/local/bin/start-docker << 'SCRIPT'
#!/bin/bash
echo "Starting Docker service..."
systemctl start docker
echo "Docker is now running. It will stop on next reboot."
echo "To check status: systemctl status docker"
SCRIPT

chmod +x /usr/local/bin/start-docker

echo ""
echo "======================================"
echo "Configuration Complete!"
echo "======================================"
echo ""
echo "Summary of changes:"
echo "- Disabled write caching (all writes are now synchronous)"
echo "- Updated /etc/fstab for sync mounting with 1-second commit interval"
echo "- Configured journald for RAM-only logging"
echo "- Disabled swap completely"
echo "- Disabled AUTOMATIC updates (manual updates still work)"
echo "- Disabled snap AUTO-REFRESH (manual refresh still works)"
echo "- Docker available on-demand (use: sudo start-docker)"
echo ""
echo "Manual operations still available:"
echo "- Update system: sudo apt update && sudo apt upgrade"
echo "- Install snaps: sudo snap install <package>"
echo "- Refresh snaps: sudo snap refresh"
echo "- Start Docker: sudo start-docker"
echo ""
echo "IMPORTANT NOTES:"
echo "1. System writes will be MUCH slower but safer"
echo "2. SD card wear will increase due to more frequent writes"
echo "3. Check configuration: sudo /usr/local/bin/check-power-safe"
echo "4. Original configs backed up to /root/power-safe-backup/"
echo ""
echo "Please reboot for all changes to take effect."
echo ""
echo "After reboot, you can safely cut power at any time."
